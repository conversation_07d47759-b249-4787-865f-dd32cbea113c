#!/bin/bash

# RSS分类器快速启动脚本

echo "🚀 RSS文章分类器启动脚本"
echo "=" * 40

# 检查模型文件是否存在
MODEL_FILE="src/main/resources/rss_lr_model.json"
if [ ! -f "$MODEL_FILE" ]; then
    echo "❌ 模型文件不存在，正在复制..."
    
    # 创建目录
    mkdir -p src/main/resources
    
    # 复制模型文件
    SOURCE_MODEL="/Users/<USER>/workplace/rssReco/out/rss_lr_model.json"
    if [ -f "$SOURCE_MODEL" ]; then
        cp "$SOURCE_MODEL" "$MODEL_FILE"
        echo "✅ 模型文件复制完成"
    else
        echo "❌ 错误: 源模型文件不存在 $SOURCE_MODEL"
        echo "请先运行 train_lr_model.py 生成模型文件"
        exit 1
    fi
fi

# 检查是否已编译
if [ ! -d "target" ]; then
    echo "🔨 首次运行，正在编译项目..."
    mvn clean compile
fi

echo "🚀 启动应用..."
echo "📍 访问地址: http://localhost:8080"
echo "⏹️  停止应用: Ctrl+C"
echo ""

# 启动应用
mvn spring-boot:run
