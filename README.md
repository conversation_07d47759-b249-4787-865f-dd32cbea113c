# RSS文章标题分类器 - Spring Boot Web应用

这是一个基于Spring Boot的Web应用，使用训练好的逻辑回归模型对RSS文章标题进行分类（喜欢/不喜欢）。

## 🚀 功能特性

- **批量标题分类**: 支持一次性输入多个标题进行分类
- **实时预测**: 基于TF-IDF字符n-gram特征和逻辑回归模型
- **直观界面**: 美观的Web界面，显示分类结果和概率
- **REST API**: 提供API接口供其他应用调用
- **结果导出**: 支持将分类结果导出为CSV文件

## 📋 项目结构

```
├── src/main/java/com/rss/classifier/
│   ├── RssClassifierApplication.java          # 主应用类
│   ├── controller/
│   │   └── RssClassifierController.java       # Web控制器
│   ├── service/
│   │   ├── RssClassificationService.java      # 分类服务
│   │   ├── TfIdfVectorizer.java               # TF-IDF向量化器
│   │   └── LogisticRegressionClassifier.java  # 逻辑回归分类器
│   └── model/
│       └── ModelConfig.java                   # 模型配置类
├── src/main/resources/
│   ├── templates/
│   │   ├── index.html                         # 主页面
│   │   └── result.html                        # 结果页面
│   ├── application.yml                        # 应用配置
│   └── rss_lr_model.json                     # 训练好的模型文件
└── pom.xml                                    # Maven依赖配置
```

## 🛠️ 部署步骤

### 1. 复制模型文件

将训练好的模型文件复制到resources目录：

```bash
cp /Users/<USER>/workplace/rssReco/out/rss_lr_model.json src/main/resources/
```

### 2. 编译项目

```bash
mvn clean compile
```

### 3. 运行应用

```bash
mvn spring-boot:run
```

或者打包后运行：

```bash
mvn clean package
java -jar target/rss-classifier-1.0.0.jar
```

### 4. 访问应用

打开浏览器访问：http://localhost:8080

## 🌐 API接口

### 单个标题分类
```http
POST /api/classify/single?title=标题内容
```

### 批量标题分类
```http
POST /api/classify/batch
Content-Type: application/json

["标题1", "标题2", "标题3"]
```

### 获取模型信息
```http
GET /api/model/info
```

## 📊 模型信息

- **算法**: 逻辑回归 (Logistic Regression)
- **特征**: TF-IDF字符n-gram (2-5)
- **特征维度**: 166,532
- **训练样本**: 21,891
- **测试样本**: 5,473
- **准确率**: 69.36%
- **ROC-AUC**: 0.7347

## 🎯 使用方法

1. **Web界面使用**:
   - 访问主页 http://localhost:8080
   - 在文本框中输入RSS文章标题（每行一个）
   - 点击"开始分类"按钮
   - 查看分类结果和概率

2. **API调用示例**:
   ```bash
   # 单个标题分类
   curl -X POST "http://localhost:8080/api/classify/single?title=人工智能技术的最新发展"
   
   # 批量分类
   curl -X POST "http://localhost:8080/api/classify/batch" \
        -H "Content-Type: application/json" \
        -d '["深度学习入门教程", "Spring Boot开发指南"]'
   ```

## 🔧 技术栈

- **后端**: Spring Boot 3.2.0, Java 17
- **前端**: Thymeleaf, Bootstrap 5.1.3
- **机器学习**: 自实现的TF-IDF向量化器和逻辑回归分类器
- **构建工具**: Maven

## 📝 注意事项

1. **模型文件**: 确保 `rss_lr_model.json` 文件存在于 `src/main/resources/` 目录中
2. **内存要求**: 模型文件较大（约12.5MB），确保有足够的内存
3. **字符编码**: 应用已配置UTF-8编码，支持中文标题
4. **性能**: 首次加载模型可能需要几秒钟时间

## 🚀 扩展功能

- 支持模型热更新
- 添加分类历史记录
- 支持更多文件格式导入
- 添加模型性能监控
- 支持多模型对比

## 📄 许可证

MIT License
