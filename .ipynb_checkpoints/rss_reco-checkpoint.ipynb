#%%
# train_test_split.py
from __future__ import annotations
import os, random, csv
from pathlib import Path
import pandas as pd
from sklearn.model_selection import train_test_split

POS_PATH = "/Users/<USER>/workplace/rssReco/ttrss_like.csv"
NEG_PATH = "/Users/<USER>/workplace/rssReco/ttrss_unlike.csv"
OUT_DIR = "/Users/<USER>/workplace/rssReco/out"
SEED = 20250727

os.makedirs(OUT_DIR, exist_ok=True)
random.seed(SEED)

def load_file(path: str, default_label: int|None) -> list[tuple[str,int]]:
    rows = []
    with open(path, "r", encoding="utf-8") as f:
        for line in f:
            line = line.strip()
            if not line:
                continue
            if "$$$" in line:
                title, lbl = line.rsplit("$$$", 1)
                try:
                    y = int(lbl)
                except:
                    continue
                rows.append((title.strip(), y))
            else:
                if default_label is None:
                    continue
                rows.append((line, default_label))
    return rows

data = []
data += load_file(POS_PATH, default_label=1)
data += load_file(NEG_PATH, default_label=0)

df = pd.DataFrame(data, columns=["title","label"]).dropna()
df = df.drop_duplicates(subset=["title"])
print("Total samples:", len(df), "pos:", df["label"].sum(), "neg:", (1-df["label"]).sum())

# 按标签分层划分（可调 test_size）
train_df, test_df = train_test_split(
    df, test_size=0.2, random_state=SEED, stratify=df["label"]
)

train_df.to_csv(Path(OUT_DIR, "train.csv"), index=False)
test_df.to_csv(Path(OUT_DIR, "test.csv"), index=False)
print("Saved:", str(Path(OUT_DIR, "train.csv")), str(Path(OUT_DIR, "test.csv")))

#%%

#%% md

#%%
