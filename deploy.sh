#!/bin/bash

# RSS文章分类器部署脚本

echo "🚀 开始部署RSS文章分类器..."

# 检查Java环境
if ! command -v java &> /dev/null; then
    echo "❌ 错误: 未找到Java环境，请先安装Java 17或更高版本"
    exit 1
fi

# 检查Maven环境
if ! command -v mvn &> /dev/null; then
    echo "❌ 错误: 未找到Maven环境，请先安装Maven"
    exit 1
fi

# 创建resources目录（如果不存在）
mkdir -p src/main/resources

# 复制模型文件
MODEL_SOURCE="/Users/<USER>/workplace/rssReco/out/rss_lr_model.json"
MODEL_TARGET="src/main/resources/rss_lr_model.json"

if [ -f "$MODEL_SOURCE" ]; then
    echo "📋 复制模型文件..."
    cp "$MODEL_SOURCE" "$MODEL_TARGET"
    echo "✅ 模型文件复制完成"
else
    echo "❌ 错误: 未找到模型文件 $MODEL_SOURCE"
    echo "请先运行 train_lr_model.py 生成模型文件"
    exit 1
fi

# 检查模型文件大小
MODEL_SIZE=$(du -h "$MODEL_TARGET" | cut -f1)
echo "📊 模型文件大小: $MODEL_SIZE"

# 编译项目
echo "🔨 编译项目..."
mvn clean compile

if [ $? -eq 0 ]; then
    echo "✅ 项目编译成功"
else
    echo "❌ 项目编译失败"
    exit 1
fi

# 打包项目
echo "📦 打包项目..."
mvn clean package -DskipTests

if [ $? -eq 0 ]; then
    echo "✅ 项目打包成功"
else
    echo "❌ 项目打包失败"
    exit 1
fi

# 检查JAR文件
JAR_FILE="target/rss-classifier-1.0.0.jar"
if [ -f "$JAR_FILE" ]; then
    JAR_SIZE=$(du -h "$JAR_FILE" | cut -f1)
    echo "📦 JAR文件大小: $JAR_SIZE"
else
    echo "❌ 错误: 未找到JAR文件"
    exit 1
fi

echo ""
echo "🎉 部署完成！"
echo ""
echo "📋 启动方式："
echo "  方式1 (开发模式): mvn spring-boot:run"
echo "  方式2 (生产模式): java -jar $JAR_FILE"
echo ""
echo "🌐 访问地址: http://localhost:8080"
echo ""
echo "📊 模型信息:"
echo "  - 特征维度: 166,532"
echo "  - 训练样本: 21,891"
echo "  - 测试样本: 5,473"
echo "  - 准确率: 69.36%"
echo ""
echo "🔌 API接口:"
echo "  - 单个分类: POST /api/classify/single?title=标题"
echo "  - 批量分类: POST /api/classify/batch"
echo "  - 模型信息: GET /api/model/info"
echo ""

# 询问是否立即启动
read -p "是否立即启动应用? (y/n): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    echo "🚀 启动应用..."
    java -jar "$JAR_FILE"
else
    echo "👋 部署完成，您可以稍后手动启动应用"
fi
