<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>RSS文章标题分类器</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .model-info {
            background-color: #f8f9fa;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 20px;
        }
        .example-titles {
            background-color: #e9ecef;
            border-radius: 5px;
            padding: 10px;
            font-size: 0.9em;
        }
        .form-container {
            max-width: 800px;
            margin: 0 auto;
        }
    </style>
</head>
<body>
    <div class="container mt-4">
        <div class="form-container">
            <h1 class="text-center mb-4">🤖 RSS文章标题分类器</h1>
            
            <!-- 模型信息 -->
            <div class="model-info" th:if="${modelInfo}">
                <h5>📊 模型信息</h5>
                <div class="row">
                    <div class="col-md-6">
                        <small class="text-muted">
                            <strong>特征维度:</strong> <span th:text="${modelInfo.featureDim}"></span><br>
                            <strong>训练样本:</strong> <span th:text="${modelInfo.trainSize}"></span>
                        </small>
                    </div>
                    <div class="col-md-6">
                        <small class="text-muted">
                            <strong>测试样本:</strong> <span th:text="${modelInfo.testSize}"></span><br>
                            <strong>准确率:</strong> <span th:text="${modelInfo.formattedAccuracy}"></span>
                        </small>
                    </div>
                </div>
            </div>
            
            <!-- 错误信息 -->
            <div class="alert alert-danger" th:if="${error}" th:text="${error}"></div>
            
            <!-- 分类表单 -->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">📝 批量标题分类</h5>
                </div>
                <div class="card-body">
                    <form action="/classify" method="post">
                        <div class="mb-3">
                            <label for="titles" class="form-label">请输入RSS文章标题（每行一个）:</label>
                            <textarea class="form-control" id="titles" name="titles" rows="10" 
                                      placeholder="请在这里输入文章标题，每行一个标题..." required></textarea>
                        </div>
                        
                        <!-- 示例标题 -->
                        <div class="example-titles mb-3">
                            <strong>💡 示例标题:</strong><br>
                            <small>
                                人工智能技术的最新发展趋势<br>
                                如何提高工作效率的10个技巧<br>
                                深度学习在图像识别中的应用<br>
                                Spring Boot开发实战指南<br>
                                机器学习算法详解与实践
                            </small>
                        </div>
                        
                        <div class="d-grid">
                            <button type="submit" class="btn btn-primary btn-lg">
                                🚀 开始分类
                            </button>
                        </div>
                    </form>
                </div>
            </div>
            
            <!-- 使用说明 -->
            <div class="card mt-4">
                <div class="card-header">
                    <h6 class="mb-0">📖 使用说明</h6>
                </div>
                <div class="card-body">
                    <ul class="mb-0">
                        <li>在文本框中输入RSS文章标题，每行一个标题</li>
                        <li>点击"开始分类"按钮进行批量分类</li>
                        <li>系统会预测每个标题是"喜欢"还是"不喜欢"</li>
                        <li>结果页面会显示详细的分类结果和概率</li>
                    </ul>
                </div>
            </div>
            
            <!-- API说明 -->
            <div class="card mt-4">
                <div class="card-header">
                    <h6 class="mb-0">🔌 API接口</h6>
                </div>
                <div class="card-body">
                    <small class="text-muted">
                        <strong>单个分类:</strong> POST /api/classify/single?title=标题<br>
                        <strong>批量分类:</strong> POST /api/classify/batch (JSON数组)<br>
                        <strong>模型信息:</strong> GET /api/model/info
                    </small>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- 快速填充示例的JavaScript -->
    <script>
        function fillExample() {
            const exampleTitles = `人工智能技术的最新发展趋势
如何提高工作效率的10个技巧
深度学习在图像识别中的应用
Spring Boot开发实战指南
机器学习算法详解与实践
Python数据分析入门教程
区块链技术原理与应用
云计算服务架构设计
前端开发最佳实践
数据库优化技巧分享`;
            
            document.getElementById('titles').value = exampleTitles;
        }
        
        // 添加快速填充按钮
        document.addEventListener('DOMContentLoaded', function() {
            const textarea = document.getElementById('titles');
            const button = document.createElement('button');
            button.type = 'button';
            button.className = 'btn btn-outline-secondary btn-sm mt-2';
            button.textContent = '📋 填充示例';
            button.onclick = fillExample;
            
            textarea.parentNode.appendChild(button);
        });
    </script>
</body>
</html>
