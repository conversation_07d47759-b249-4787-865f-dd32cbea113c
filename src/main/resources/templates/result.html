<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>分类结果 - RSS文章标题分类器</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .result-container {
            max-width: 1000px;
            margin: 0 auto;
        }
        .like-result {
            background-color: #d4edda;
            border-left: 4px solid #28a745;
        }
        .dislike-result {
            background-color: #f8d7da;
            border-left: 4px solid #dc3545;
        }
        .stats-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        .probability-bar {
            height: 8px;
            border-radius: 4px;
            background-color: #e9ecef;
            overflow: hidden;
        }
        .probability-fill {
            height: 100%;
            transition: width 0.3s ease;
        }
        .like-bar { background-color: #28a745; }
        .dislike-bar { background-color: #dc3545; }
    </style>
</head>
<body>
    <div class="container mt-4">
        <div class="result-container">
            <h1 class="text-center mb-4">📊 分类结果</h1>
            
            <!-- 统计信息 -->
            <div class="row mb-4">
                <div class="col-md-4">
                    <div class="card stats-card">
                        <div class="card-body text-center">
                            <h3 th:text="${totalCount}">0</h3>
                            <p class="mb-0">总标题数</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card bg-success text-white">
                        <div class="card-body text-center">
                            <h3 th:text="${likeCount}">0</h3>
                            <p class="mb-0">😍 喜欢</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card bg-danger text-white">
                        <div class="card-body text-center">
                            <h3 th:text="${dislikeCount}">0</h3>
                            <p class="mb-0">😐 不喜欢</p>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 模型信息 -->
            <div class="card mb-4" th:if="${modelInfo}">
                <div class="card-header">
                    <h6 class="mb-0">🤖 模型信息</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3">
                            <small><strong>准确率:</strong> <span th:text="${modelInfo.formattedAccuracy}"></span></small>
                        </div>
                        <div class="col-md-3">
                            <small><strong>ROC-AUC:</strong> <span th:text="${modelInfo.formattedRocAuc}"></span></small>
                        </div>
                        <div class="col-md-3">
                            <small><strong>特征维度:</strong> <span th:text="${modelInfo.featureDim}"></span></small>
                        </div>
                        <div class="col-md-3">
                            <small><strong>训练样本:</strong> <span th:text="${modelInfo.trainSize}"></span></small>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 详细结果 -->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">📝 详细分类结果</h5>
                </div>
                <div class="card-body">
                    <div class="row" th:each="result, iterStat : ${results}">
                        <div class="col-12 mb-3">
                            <div class="card" 
                                 th:classappend="${result.prediction == 1} ? 'like-result' : 'dislike-result'">
                                <div class="card-body">
                                    <div class="row align-items-center">
                                        <div class="col-md-1 text-center">
                                            <span class="badge fs-6" 
                                                  th:classappend="${result.prediction == 1} ? 'bg-success' : 'bg-danger'"
                                                  th:text="${iterStat.count}"></span>
                                        </div>
                                        <div class="col-md-7">
                                            <h6 class="mb-1" th:text="${result.title}"></h6>
                                            <div class="probability-bar">
                                                <div class="probability-fill" 
                                                     th:classappend="${result.prediction == 1} ? 'like-bar' : 'dislike-bar'"
                                                     th:style="'width: ' + ${result.probability * 100} + '%'"></div>
                                            </div>
                                        </div>
                                        <div class="col-md-2 text-center">
                                            <span class="badge fs-6" 
                                                  th:classappend="${result.prediction == 1} ? 'bg-success' : 'bg-danger'"
                                                  th:text="${result.label}"></span>
                                        </div>
                                        <div class="col-md-2 text-center">
                                            <small class="text-muted">
                                                <strong th:text="${result.formattedProbability}"></strong>
                                            </small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 操作按钮 -->
            <div class="row mt-4">
                <div class="col-md-6">
                    <a href="/" class="btn btn-primary btn-lg w-100">
                        🔄 重新分类
                    </a>
                </div>
                <div class="col-md-6">
                    <button type="button" class="btn btn-success btn-lg w-100" onclick="exportResults()">
                        📥 导出结果
                    </button>
                </div>
            </div>
            
            <!-- 返回输入的表单（隐藏） -->
            <form action="/classify" method="post" style="display: none;" id="retryForm">
                <textarea name="titles" th:text="${inputTitles}"></textarea>
            </form>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // 导出结果为CSV
        function exportResults() {
            const results = [];
            results.push(['序号', '标题', '预测结果', '概率', '标签']);
            
            document.querySelectorAll('.card-body .row').forEach((row, index) => {
                if (row.querySelector('.badge')) {
                    const title = row.querySelector('h6').textContent;
                    const label = row.querySelector('.badge:last-of-type').textContent;
                    const probability = row.querySelector('strong').textContent;
                    const prediction = label === '喜欢' ? 1 : 0;
                    
                    results.push([index + 1, title, prediction, probability, label]);
                }
            });
            
            const csvContent = results.map(row => 
                row.map(field => `"${field}"`).join(',')
            ).join('\n');
            
            const blob = new Blob(['\ufeff' + csvContent], { type: 'text/csv;charset=utf-8;' });
            const link = document.createElement('a');
            const url = URL.createObjectURL(blob);
            link.setAttribute('href', url);
            link.setAttribute('download', 'rss_classification_results.csv');
            link.style.visibility = 'hidden';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        }
        
        // 添加动画效果
        document.addEventListener('DOMContentLoaded', function() {
            const cards = document.querySelectorAll('.card-body .row .card');
            cards.forEach((card, index) => {
                setTimeout(() => {
                    card.style.opacity = '0';
                    card.style.transform = 'translateY(20px)';
                    card.style.transition = 'all 0.3s ease';
                    
                    setTimeout(() => {
                        card.style.opacity = '1';
                        card.style.transform = 'translateY(0)';
                    }, 50);
                }, index * 100);
            });
        });
    </script>
</body>
</html>
