package com.rss.classifier.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.rss.classifier.model.ModelConfig;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Service;

import jakarta.annotation.PostConstruct;
import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.List;

/**
 * RSS文章分类服务
 */
@Service
public class RssClassificationService {
    
    @Autowired
    private TfIdfVectorizer vectorizer;
    
    @Autowired
    private LogisticRegressionClassifier classifier;
    
    private ModelConfig modelConfig;
    
    /**
     * 初始化模型
     */
    @PostConstruct
    public void initModel() {
        try {
            // 从classpath加载模型文件
            ClassPathResource resource = new ClassPathResource("rss_lr_model.json");
            ObjectMapper mapper = new ObjectMapper();
            
            try (InputStream inputStream = resource.getInputStream()) {
                modelConfig = mapper.readValue(inputStream, ModelConfig.class);
            }
            
            // 配置向量化器和分类器
            vectorizer.loadConfig(modelConfig.getVectorizer());
            classifier.loadConfig(modelConfig.getModel());
            
            System.out.println("模型加载成功!");
            System.out.println("特征维度: " + modelConfig.getMeta().getFeatureDim());
            System.out.println("训练准确率: " + modelConfig.getMeta().getMetrics().getAccuracy());
            
        } catch (IOException e) {
            System.err.println("模型加载失败: " + e.getMessage());
            throw new RuntimeException("Failed to load model", e);
        }
    }
    
    /**
     * 单个标题分类
     */
    public ClassificationResult classifyTitle(String title) {
        if (title == null || title.trim().isEmpty()) {
            return new ClassificationResult(title, 0, 0.0, "不喜欢");
        }
        
        // 向量化
        double[] features = vectorizer.transform(title.trim());
        
        // 预测
        double probability = classifier.predictProba(features);
        int prediction = classifier.predict(features);
        String label = prediction == 1 ? "喜欢" : "不喜欢";
        
        return new ClassificationResult(title, prediction, probability, label);
    }
    
    /**
     * 批量标题分类
     */
    public List<ClassificationResult> classifyTitles(List<String> titles) {
        List<ClassificationResult> results = new ArrayList<>();
        
        for (String title : titles) {
            results.add(classifyTitle(title));
        }
        
        return results;
    }
    
    /**
     * 获取模型信息
     */
    public ModelInfo getModelInfo() {
        if (modelConfig == null) {
            return null;
        }
        
        return new ModelInfo(
            modelConfig.getMeta().getFeatureDim(),
            modelConfig.getMeta().getTrainSize(),
            modelConfig.getMeta().getTestSize(),
            modelConfig.getMeta().getMetrics().getAccuracy(),
            modelConfig.getMeta().getMetrics().getRocAuc()
        );
    }
    
    /**
     * 分类结果类
     */
    public static class ClassificationResult {
        private String title;
        private int prediction;
        private double probability;
        private String label;
        
        public ClassificationResult(String title, int prediction, double probability, String label) {
            this.title = title;
            this.prediction = prediction;
            this.probability = probability;
            this.label = label;
        }
        
        // Getters
        public String getTitle() { return title; }
        public int getPrediction() { return prediction; }
        public double getProbability() { return probability; }
        public String getLabel() { return label; }
        public String getFormattedProbability() { 
            return String.format("%.2f%%", probability * 100); 
        }
    }
    
    /**
     * 模型信息类
     */
    public static class ModelInfo {
        private int featureDim;
        private int trainSize;
        private int testSize;
        private double accuracy;
        private Double rocAuc;
        
        public ModelInfo(int featureDim, int trainSize, int testSize, double accuracy, Double rocAuc) {
            this.featureDim = featureDim;
            this.trainSize = trainSize;
            this.testSize = testSize;
            this.accuracy = accuracy;
            this.rocAuc = rocAuc;
        }
        
        // Getters
        public int getFeatureDim() { return featureDim; }
        public int getTrainSize() { return trainSize; }
        public int getTestSize() { return testSize; }
        public double getAccuracy() { return accuracy; }
        public Double getRocAuc() { return rocAuc; }
        public String getFormattedAccuracy() { 
            return String.format("%.2f%%", accuracy * 100); 
        }
        public String getFormattedRocAuc() { 
            return rocAuc != null ? String.format("%.4f", rocAuc) : "N/A"; 
        }
    }
}
