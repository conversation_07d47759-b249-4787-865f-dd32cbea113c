package com.rss.classifier.service;

import com.rss.classifier.model.ModelConfig;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * TF-IDF向量化器，复现Python scikit-learn的TfidfVectorizer
 */
@Component
public class TfIdfVectorizer {
    
    private ModelConfig.Vectorizer config;
    
    public void loadConfig(ModelConfig.Vectorizer config) {
        this.config = config;
    }
    
    /**
     * 将文本转换为TF-IDF特征向量
     */
    public double[] transform(String text) {
        if (config == null) {
            throw new IllegalStateException("Vectorizer not configured");
        }
        
        // 生成字符n-gram
        List<String> ngrams = generateCharNgrams(text, config.getNgramMin(), config.getNgramMax());
        
        // 计算词频
        Map<String, Integer> termFreq = new HashMap<>();
        for (String ngram : ngrams) {
            termFreq.put(ngram, termFreq.getOrDefault(ngram, 0) + 1);
        }
        
        // 创建特征向量
        double[] features = new double[config.getVocabulary().size()];
        
        for (Map.Entry<String, Integer> entry : termFreq.entrySet()) {
            String ngram = entry.getKey();
            int freq = entry.getValue();
            
            Integer vocabIndex = config.getVocabulary().get(ngram);
            if (vocabIndex != null) {
                // TF-IDF计算: tf * idf
                double tf = freq; // 不使用sublinear_tf
                double idf = config.getIdf().get(vocabIndex);
                features[vocabIndex] = tf * idf;
            }
        }
        
        // L2归一化
        if ("l2".equals(config.getNorm())) {
            l2Normalize(features);
        }
        
        return features;
    }
    
    /**
     * 生成字符级n-gram
     */
    private List<String> generateCharNgrams(String text, int minN, int maxN) {
        List<String> ngrams = new ArrayList<>();
        
        if (text == null || text.isEmpty()) {
            return ngrams;
        }
        
        for (int n = minN; n <= maxN; n++) {
            for (int i = 0; i <= text.length() - n; i++) {
                String ngram = text.substring(i, i + n);
                ngrams.add(ngram);
            }
        }
        
        return ngrams;
    }
    
    /**
     * L2归一化
     */
    private void l2Normalize(double[] vector) {
        double norm = 0.0;
        for (double value : vector) {
            norm += value * value;
        }
        norm = Math.sqrt(norm);
        
        if (norm > 0) {
            for (int i = 0; i < vector.length; i++) {
                vector[i] /= norm;
            }
        }
    }
}
