package com.rss.classifier.controller;

import com.rss.classifier.service.RssClassificationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * RSS分类控制器
 */
@Controller
public class RssClassifierController {
    
    @Autowired
    private RssClassificationService classificationService;
    
    /**
     * 主页面
     */
    @GetMapping("/")
    public String index(Model model) {
        // 添加模型信息到页面
        RssClassificationService.ModelInfo modelInfo = classificationService.getModelInfo();
        model.addAttribute("modelInfo", modelInfo);
        return "index";
    }
    
    /**
     * 批量分类处理
     */
    @PostMapping("/classify")
    public String classify(@RequestParam("titles") String titlesText, Model model) {
        // 解析输入的标题（按行分割）
        List<String> titles = Arrays.stream(titlesText.split("\n"))
                .map(String::trim)
                .filter(title -> !title.isEmpty())
                .collect(Collectors.toList());
        
        if (titles.isEmpty()) {
            model.addAttribute("error", "请输入至少一个标题");
            model.addAttribute("modelInfo", classificationService.getModelInfo());
            return "index";
        }
        
        // 执行分类
        List<RssClassificationService.ClassificationResult> results = 
                classificationService.classifyTitles(titles);
        
        // 统计结果
        long likeCount = results.stream().mapToLong(r -> r.getPrediction()).sum();
        long dislikeCount = results.size() - likeCount;
        
        // 添加结果到模型
        model.addAttribute("results", results);
        model.addAttribute("totalCount", results.size());
        model.addAttribute("likeCount", likeCount);
        model.addAttribute("dislikeCount", dislikeCount);
        model.addAttribute("inputTitles", titlesText);
        model.addAttribute("modelInfo", classificationService.getModelInfo());
        
        return "result";
    }
    
    /**
     * REST API - 单个标题分类
     */
    @PostMapping("/api/classify/single")
    @ResponseBody
    public RssClassificationService.ClassificationResult classifySingle(@RequestParam("title") String title) {
        return classificationService.classifyTitle(title);
    }
    
    /**
     * REST API - 批量标题分类
     */
    @PostMapping("/api/classify/batch")
    @ResponseBody
    public List<RssClassificationService.ClassificationResult> classifyBatch(@RequestBody List<String> titles) {
        return classificationService.classifyTitles(titles);
    }
    
    /**
     * REST API - 获取模型信息
     */
    @GetMapping("/api/model/info")
    @ResponseBody
    public RssClassificationService.ModelInfo getModelInfo() {
        return classificationService.getModelInfo();
    }
}
