package com.rss.classifier.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.Map;
import java.util.List;

/**
 * 模型配置类，对应JSON文件结构
 */
@Data
public class ModelConfig {
    
    private Vectorizer vectorizer;
    private Model model;
    private Meta meta;
    
    @Data
    public static class Vectorizer {
        private String type;
        @JsonProperty("ngram_min")
        private int ngramMin;
        @JsonProperty("ngram_max")
        private int ngramMax;
        @JsonProperty("min_df")
        private int minDf;
        private String norm;
        @JsonProperty("use_idf")
        private boolean useIdf;
        @JsonProperty("smooth_idf")
        private boolean smoothIdf;
        @JsonProperty("sublinear_tf")
        private boolean sublinearTf;
        private Map<String, Integer> vocabulary;
        private List<Double> idf;
    }
    
    @Data
    public static class Model {
        private String type;
        private List<Double> coef;
        private double intercept;
    }
    
    @Data
    public static class Meta {
        @JsonProperty("train_size")
        private int trainSize;
        @JsonProperty("test_size")
        private int testSize;
        private int seed;
        @JsonProperty("feature_dim")
        private int featureDim;
        private Metrics metrics;
        
        @Data
        public static class Metrics {
            private double accuracy;
            @JsonProperty("roc_auc")
            private Double rocAuc;
        }
    }
}
