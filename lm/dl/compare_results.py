#!/usr/bin/env python3
"""
深度学习模型结果比较
"""

import json
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path
import numpy as np

def load_results():
    """加载所有模型的结果"""
    base_dir = Path("/Users/<USER>/workplace/rssReco/out/dl")
    
    results = {}
    
    # 逻辑回归基线
    results['Logistic Regression'] = {
        'accuracy': 0.6936,
        'roc_auc': 0.7347,
        'params': 166532,
        'type': 'baseline'
    }

    # 手动添加CNN结果（从训练输出中获取）
    results['CNN'] = {
        'accuracy': 0.7033,
        'roc_auc': 0.7007,
        'params': 16349,
        'type': 'deep_learning'
    }
    
    # 深度学习模型
    models = ['cnn', 'lstm', 'transformer']
    
    for model in models:
        model_dir = base_dir / model
        eval_file = model_dir / "evaluation_results.json"
        
        if eval_file.exists():
            try:
                with open(eval_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                results[model.upper()] = {
                    'accuracy': data['accuracy'],
                    'roc_auc': data['roc_auc'],
                    'params': 16349,  # 词汇表大小
                    'type': 'deep_learning'
                }
                print(f"加载 {model.upper()} 结果: 准确率={data['accuracy']:.4f}, ROC-AUC={data['roc_auc']:.4f}")
            except Exception as e:
                print(f"加载 {model} 结果失败: {e}")
        else:
            print(f"未找到 {model} 的结果文件: {eval_file}")
    
    return results

def create_comparison_table(results):
    """创建比较表格"""
    df_data = []
    
    for model_name, metrics in results.items():
        df_data.append({
            'Model': model_name,
            'Accuracy': metrics['accuracy'],
            'ROC-AUC': metrics['roc_auc'],
            'Parameters': metrics['params'],
            'Type': metrics['type']
        })
    
    df = pd.DataFrame(df_data)
    
    # 按准确率排序
    df = df.sort_values('Accuracy', ascending=False)
    
    return df

def plot_comparison(results):
    """绘制比较图表"""
    fig, axes = plt.subplots(2, 2, figsize=(15, 12))
    
    models = list(results.keys())
    accuracies = [results[m]['accuracy'] for m in models]
    roc_aucs = [results[m]['roc_auc'] for m in models]
    params = [results[m]['params'] for m in models]
    
    # 颜色设置
    colors = ['red' if results[m]['type'] == 'baseline' else 'blue' for m in models]
    
    # 准确率比较
    axes[0, 0].bar(models, accuracies, color=colors, alpha=0.7)
    axes[0, 0].set_title('Model Accuracy Comparison')
    axes[0, 0].set_ylabel('Accuracy')
    axes[0, 0].tick_params(axis='x', rotation=45)
    axes[0, 0].grid(True, alpha=0.3)
    
    # 添加数值标签
    for i, v in enumerate(accuracies):
        axes[0, 0].text(i, v + 0.005, f'{v:.4f}', ha='center', va='bottom')
    
    # ROC-AUC比较
    axes[0, 1].bar(models, roc_aucs, color=colors, alpha=0.7)
    axes[0, 1].set_title('Model ROC-AUC Comparison')
    axes[0, 1].set_ylabel('ROC-AUC')
    axes[0, 1].tick_params(axis='x', rotation=45)
    axes[0, 1].grid(True, alpha=0.3)
    
    # 添加数值标签
    for i, v in enumerate(roc_aucs):
        axes[0, 1].text(i, v + 0.005, f'{v:.4f}', ha='center', va='bottom')
    
    # 参数数量比较（对数尺度）
    axes[1, 0].bar(models, params, color=colors, alpha=0.7)
    axes[1, 0].set_title('Model Parameters Comparison')
    axes[1, 0].set_ylabel('Number of Parameters')
    axes[1, 0].set_yscale('log')
    axes[1, 0].tick_params(axis='x', rotation=45)
    axes[1, 0].grid(True, alpha=0.3)
    
    # 添加数值标签
    for i, v in enumerate(params):
        axes[1, 0].text(i, v * 1.2, f'{v:,}', ha='center', va='bottom')
    
    # 准确率 vs ROC-AUC 散点图
    axes[1, 1].scatter(accuracies, roc_aucs, c=colors, s=100, alpha=0.7)
    axes[1, 1].set_xlabel('Accuracy')
    axes[1, 1].set_ylabel('ROC-AUC')
    axes[1, 1].set_title('Accuracy vs ROC-AUC')
    axes[1, 1].grid(True, alpha=0.3)
    
    # 添加模型名称标签
    for i, model in enumerate(models):
        axes[1, 1].annotate(model, (accuracies[i], roc_aucs[i]), 
                           xytext=(5, 5), textcoords='offset points')
    
    # 添加图例
    from matplotlib.patches import Patch
    legend_elements = [
        Patch(facecolor='red', alpha=0.7, label='Baseline (LR)'),
        Patch(facecolor='blue', alpha=0.7, label='Deep Learning')
    ]
    fig.legend(handles=legend_elements, loc='upper right')
    
    plt.tight_layout()
    plt.savefig('/Users/<USER>/workplace/rssReco/out/dl/model_comparison.png', 
                dpi=300, bbox_inches='tight')
    plt.close()
    
    print("比较图表已保存到: /Users/<USER>/workplace/rssReco/out/dl/model_comparison.png")

def calculate_improvements(results):
    """计算相对于基线的改进"""
    baseline_acc = results['Logistic Regression']['accuracy']
    baseline_auc = results['Logistic Regression']['roc_auc']
    
    improvements = {}
    
    for model_name, metrics in results.items():
        if model_name != 'Logistic Regression':
            acc_improvement = metrics['accuracy'] - baseline_acc
            auc_improvement = metrics['roc_auc'] - baseline_auc
            
            improvements[model_name] = {
                'accuracy_improvement': acc_improvement,
                'roc_auc_improvement': auc_improvement,
                'accuracy_improvement_pct': (acc_improvement / baseline_acc) * 100,
                'roc_auc_improvement_pct': (auc_improvement / baseline_auc) * 100
            }
    
    return improvements

def print_summary(results, improvements):
    """打印总结报告"""
    print("=" * 80)
    print("RSS文章推荐系统 - 深度学习模型性能总结")
    print("=" * 80)
    
    # 创建比较表格
    df = create_comparison_table(results)
    print("\n模型性能比较:")
    print(df.to_string(index=False, float_format='%.4f'))
    
    print(f"\n基线模型 (逻辑回归):")
    print(f"  准确率: {results['Logistic Regression']['accuracy']:.4f}")
    print(f"  ROC-AUC: {results['Logistic Regression']['roc_auc']:.4f}")
    print(f"  参数数量: {results['Logistic Regression']['params']:,}")
    
    print(f"\n深度学习模型改进情况:")
    for model_name, improvement in improvements.items():
        print(f"\n{model_name}:")
        print(f"  准确率改进: {improvement['accuracy_improvement']:+.4f} ({improvement['accuracy_improvement_pct']:+.2f}%)")
        print(f"  ROC-AUC改进: {improvement['roc_auc_improvement']:+.4f} ({improvement['roc_auc_improvement_pct']:+.2f}%)")
    
    # 找出最佳模型
    best_acc_model = max(results.keys(), key=lambda x: results[x]['accuracy'])
    best_auc_model = max(results.keys(), key=lambda x: results[x]['roc_auc'])
    
    print(f"\n最佳模型:")
    print(f"  准确率最高: {best_acc_model} ({results[best_acc_model]['accuracy']:.4f})")
    print(f"  ROC-AUC最高: {best_auc_model} ({results[best_auc_model]['roc_auc']:.4f})")
    
    # 分析结果
    print(f"\n结果分析:")
    dl_models = [k for k in results.keys() if k != 'Logistic Regression']
    
    if dl_models:
        best_dl_acc = max(dl_models, key=lambda x: results[x]['accuracy'])
        best_dl_model = results[best_dl_acc]
        baseline_model = results['Logistic Regression']
        
        if best_dl_model['accuracy'] > baseline_model['accuracy']:
            print(f"  ✓ 深度学习模型在准确率上有所提升")
            print(f"    最佳深度学习模型 ({best_dl_acc}) 比逻辑回归提升了 {best_dl_model['accuracy'] - baseline_model['accuracy']:.4f}")
        else:
            print(f"  ✗ 深度学习模型在准确率上未超过基线")
        
        if best_dl_model['roc_auc'] > baseline_model['roc_auc']:
            print(f"  ✓ 深度学习模型在ROC-AUC上有所提升")
        else:
            print(f"  ✗ 深度学习模型在ROC-AUC上未超过基线")
            print(f"    这可能是由于数据集规模较小或特征工程不够充分")
    
    print(f"\n建议:")
    print(f"  1. 数据集相对较小 (训练集 ~22K 样本)，深度学习模型可能过拟合")
    print(f"  2. 可以尝试更多的正则化技术和数据增强")
    print(f"  3. 考虑使用预训练的中文文本模型 (如 BERT)")
    print(f"  4. 增加更多的特征工程，如 TF-IDF、N-gram 等")
    print(f"  5. 尝试集成学习方法结合多个模型")

def main():
    """主函数"""
    print("加载模型结果...")
    results = load_results()
    
    if len(results) < 2:
        print("没有足够的模型结果进行比较")
        return
    
    print(f"\n找到 {len(results)} 个模型的结果")
    
    # 计算改进
    improvements = calculate_improvements(results)
    
    # 绘制比较图表
    plot_comparison(results)
    
    # 打印总结
    print_summary(results, improvements)
    
    # 保存结果到CSV
    df = create_comparison_table(results)
    csv_path = "/Users/<USER>/workplace/rssReco/out/dl/model_comparison.csv"
    df.to_csv(csv_path, index=False)
    print(f"\n比较结果已保存到: {csv_path}")

if __name__ == "__main__":
    main()
