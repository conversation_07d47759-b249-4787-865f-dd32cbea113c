#!/usr/bin/env python3
"""
深度学习模型训练器
"""

import os
import time
import json
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader
from sklearn.metrics import accuracy_score, roc_auc_score, classification_report, confusion_matrix
import numpy as np
from pathlib import Path
from typing import Dict, List, Optional, Tuple
from tqdm import tqdm
import matplotlib.pyplot as plt
import seaborn as sns

class Trainer:
    """模型训练器"""
    
    def __init__(self, model: nn.Module, device: str = 'auto', 
                 output_dir: str = "/Users/<USER>/workplace/rssReco/out/dl"):
        
        # 设备选择
        if device == 'auto':
            self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        else:
            self.device = torch.device(device)
        
        self.model = model.to(self.device)
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        # 训练历史
        self.train_history = {
            'train_loss': [],
            'train_acc': [],
            'val_loss': [],
            'val_acc': [],
            'learning_rates': []
        }
        
        print(f"使用设备: {self.device}")
        print(f"输出目录: {self.output_dir}")
    
    def train_epoch(self, train_loader: DataLoader, optimizer: optim.Optimizer, 
                   criterion: nn.Module, epoch: int) -> Tuple[float, float]:
        """训练一个epoch"""
        
        self.model.train()
        total_loss = 0.0
        all_preds = []
        all_labels = []
        
        progress_bar = tqdm(train_loader, desc=f'Epoch {epoch+1} [Train]')
        
        for batch in progress_bar:
            # 数据移到设备
            if 'input_ids' in batch:
                # BERT模型
                input_ids = batch['input_ids'].to(self.device)
                attention_mask = batch['attention_mask'].to(self.device)
                labels = batch['label'].to(self.device)
                
                outputs = self.model(input_ids, attention_mask)
            else:
                # 其他模型
                inputs = batch['text'].to(self.device)
                labels = batch['label'].to(self.device)
                
                outputs = self.model(inputs)
            
            loss = criterion(outputs, labels)
            
            # 反向传播
            optimizer.zero_grad()
            loss.backward()
            optimizer.step()
            
            # 统计
            total_loss += loss.item()
            preds = torch.argmax(outputs, dim=1)
            all_preds.extend(preds.cpu().numpy())
            all_labels.extend(labels.cpu().numpy())
            
            # 更新进度条
            progress_bar.set_postfix({
                'loss': f'{loss.item():.4f}',
                'acc': f'{accuracy_score(all_labels, all_preds):.4f}'
            })
        
        avg_loss = total_loss / len(train_loader)
        accuracy = accuracy_score(all_labels, all_preds)
        
        return avg_loss, accuracy
    
    def validate_epoch(self, val_loader: DataLoader, criterion: nn.Module, 
                      epoch: int) -> Tuple[float, float]:
        """验证一个epoch"""
        
        self.model.eval()
        total_loss = 0.0
        all_preds = []
        all_labels = []
        
        with torch.no_grad():
            progress_bar = tqdm(val_loader, desc=f'Epoch {epoch+1} [Val]')
            
            for batch in progress_bar:
                # 数据移到设备
                if 'input_ids' in batch:
                    # BERT模型
                    input_ids = batch['input_ids'].to(self.device)
                    attention_mask = batch['attention_mask'].to(self.device)
                    labels = batch['label'].to(self.device)
                    
                    outputs = self.model(input_ids, attention_mask)
                else:
                    # 其他模型
                    inputs = batch['text'].to(self.device)
                    labels = batch['label'].to(self.device)
                    
                    outputs = self.model(inputs)
                
                loss = criterion(outputs, labels)
                
                # 统计
                total_loss += loss.item()
                preds = torch.argmax(outputs, dim=1)
                all_preds.extend(preds.cpu().numpy())
                all_labels.extend(labels.cpu().numpy())
                
                # 更新进度条
                progress_bar.set_postfix({
                    'loss': f'{loss.item():.4f}',
                    'acc': f'{accuracy_score(all_labels, all_preds):.4f}'
                })
        
        avg_loss = total_loss / len(val_loader)
        accuracy = accuracy_score(all_labels, all_preds)
        
        return avg_loss, accuracy
    
    def train(self, train_loader: DataLoader, val_loader: Optional[DataLoader] = None,
              num_epochs: int = 10, learning_rate: float = 1e-3, 
              weight_decay: float = 1e-4, patience: int = 5,
              save_best: bool = True) -> Dict:
        """完整训练流程"""
        
        # 优化器和损失函数
        optimizer = optim.AdamW(self.model.parameters(), 
                               lr=learning_rate, weight_decay=weight_decay)
        criterion = nn.CrossEntropyLoss()
        
        # 学习率调度器
        scheduler = optim.lr_scheduler.ReduceLROnPlateau(
            optimizer, mode='min', factor=0.5, patience=patience//2
        )
        
        best_val_acc = 0.0
        patience_counter = 0
        
        print(f"开始训练 - {num_epochs} epochs")
        print(f"学习率: {learning_rate}, 权重衰减: {weight_decay}")
        print(f"早停耐心: {patience}")
        
        for epoch in range(num_epochs):
            start_time = time.time()
            
            # 训练
            train_loss, train_acc = self.train_epoch(train_loader, optimizer, criterion, epoch)
            
            # 验证
            if val_loader:
                val_loss, val_acc = self.validate_epoch(val_loader, criterion, epoch)
                scheduler.step(val_loss)
            else:
                val_loss, val_acc = 0.0, 0.0
            
            # 记录历史
            self.train_history['train_loss'].append(train_loss)
            self.train_history['train_acc'].append(train_acc)
            self.train_history['val_loss'].append(val_loss)
            self.train_history['val_acc'].append(val_acc)
            self.train_history['learning_rates'].append(optimizer.param_groups[0]['lr'])
            
            epoch_time = time.time() - start_time
            
            print(f"Epoch {epoch+1}/{num_epochs} ({epoch_time:.1f}s)")
            print(f"  Train Loss: {train_loss:.4f}, Train Acc: {train_acc:.4f}")
            if val_loader:
                print(f"  Val Loss: {val_loss:.4f}, Val Acc: {val_acc:.4f}")
                print(f"  LR: {optimizer.param_groups[0]['lr']:.2e}")
            
            # 早停和保存最佳模型
            if val_loader and val_acc > best_val_acc:
                best_val_acc = val_acc
                patience_counter = 0
                
                if save_best:
                    self.save_model(f"best_model_epoch_{epoch+1}.pth")
                    print(f"  保存最佳模型 (Val Acc: {val_acc:.4f})")
            elif val_loader:
                patience_counter += 1
                
                if patience_counter >= patience:
                    print(f"早停触发 - {patience} epochs 无改善")
                    break
            
            print()
        
        # 保存训练历史
        self.save_training_history()
        
        # 绘制训练曲线
        self.plot_training_curves()
        
        return {
            'best_val_acc': best_val_acc,
            'final_train_acc': train_acc,
            'final_val_acc': val_acc if val_loader else None,
            'epochs_trained': epoch + 1
        }
    
    def evaluate(self, test_loader: DataLoader) -> Dict:
        """评估模型"""
        
        self.model.eval()
        all_preds = []
        all_labels = []
        all_probs = []
        
        with torch.no_grad():
            progress_bar = tqdm(test_loader, desc='Evaluating')
            
            for batch in progress_bar:
                # 数据移到设备
                if 'input_ids' in batch:
                    # BERT模型
                    input_ids = batch['input_ids'].to(self.device)
                    attention_mask = batch['attention_mask'].to(self.device)
                    labels = batch['label'].to(self.device)
                    
                    outputs = self.model(input_ids, attention_mask)
                else:
                    # 其他模型
                    inputs = batch['text'].to(self.device)
                    labels = batch['label'].to(self.device)
                    
                    outputs = self.model(inputs)
                
                probs = torch.softmax(outputs, dim=1)
                preds = torch.argmax(outputs, dim=1)
                
                all_preds.extend(preds.cpu().numpy())
                all_labels.extend(labels.cpu().numpy())
                all_probs.extend(probs.cpu().numpy())
        
        # 计算指标
        accuracy = accuracy_score(all_labels, all_preds)
        
        try:
            # 使用正类概率计算ROC-AUC
            pos_probs = [prob[1] for prob in all_probs]
            roc_auc = roc_auc_score(all_labels, pos_probs)
        except ValueError:
            roc_auc = None
        
        # 分类报告
        class_report = classification_report(all_labels, all_preds, output_dict=True)
        
        # 混淆矩阵
        cm = confusion_matrix(all_labels, all_preds)
        
        results = {
            'accuracy': accuracy,
            'roc_auc': roc_auc,
            'classification_report': class_report,
            'confusion_matrix': cm.tolist(),
            'predictions': all_preds,
            'labels': all_labels,
            'probabilities': all_probs
        }
        
        # 打印结果
        print("="*50)
        print("模型评估结果")
        print("="*50)
        print(f"准确率: {accuracy:.4f}")
        if roc_auc:
            print(f"ROC-AUC: {roc_auc:.4f}")
        print(f"\n混淆矩阵:")
        print(cm)
        print(f"\n分类报告:")
        print(classification_report(all_labels, all_preds, digits=4))
        
        # 保存结果
        self.save_evaluation_results(results)
        
        return results
    
    def save_model(self, filename: str):
        """保存模型"""
        filepath = self.output_dir / filename
        torch.save({
            'model_state_dict': self.model.state_dict(),
            'model_class': self.model.__class__.__name__,
            'train_history': self.train_history
        }, filepath)
        
    def load_model(self, filepath: str):
        """加载模型"""
        checkpoint = torch.load(filepath, map_location=self.device)
        self.model.load_state_dict(checkpoint['model_state_dict'])
        if 'train_history' in checkpoint:
            self.train_history = checkpoint['train_history']
    
    def save_training_history(self):
        """保存训练历史"""
        filepath = self.output_dir / "training_history.json"
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(self.train_history, f, ensure_ascii=False, indent=2)
    
    def save_evaluation_results(self, results: Dict):
        """保存评估结果"""
        # 转换numpy数组为列表以便JSON序列化
        results_copy = results.copy()
        if 'probabilities' in results_copy:
            results_copy['probabilities'] = [prob.tolist() if hasattr(prob, 'tolist') else prob
                                           for prob in results_copy['probabilities']]

        # 转换numpy int64为普通int
        if 'predictions' in results_copy:
            results_copy['predictions'] = [int(pred) for pred in results_copy['predictions']]
        if 'labels' in results_copy:
            results_copy['labels'] = [int(label) for label in results_copy['labels']]

        filepath = self.output_dir / "evaluation_results.json"
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(results_copy, f, ensure_ascii=False, indent=2)
    
    def plot_training_curves(self):
        """绘制训练曲线"""
        if not self.train_history['train_loss']:
            return
        
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        
        epochs = range(1, len(self.train_history['train_loss']) + 1)
        
        # 损失曲线
        axes[0, 0].plot(epochs, self.train_history['train_loss'], 'b-', label='Train Loss')
        if self.train_history['val_loss'] and any(self.train_history['val_loss']):
            axes[0, 0].plot(epochs, self.train_history['val_loss'], 'r-', label='Val Loss')
        axes[0, 0].set_title('Loss Curves')
        axes[0, 0].set_xlabel('Epoch')
        axes[0, 0].set_ylabel('Loss')
        axes[0, 0].legend()
        axes[0, 0].grid(True)
        
        # 准确率曲线
        axes[0, 1].plot(epochs, self.train_history['train_acc'], 'b-', label='Train Acc')
        if self.train_history['val_acc'] and any(self.train_history['val_acc']):
            axes[0, 1].plot(epochs, self.train_history['val_acc'], 'r-', label='Val Acc')
        axes[0, 1].set_title('Accuracy Curves')
        axes[0, 1].set_xlabel('Epoch')
        axes[0, 1].set_ylabel('Accuracy')
        axes[0, 1].legend()
        axes[0, 1].grid(True)
        
        # 学习率曲线
        axes[1, 0].plot(epochs, self.train_history['learning_rates'], 'g-')
        axes[1, 0].set_title('Learning Rate')
        axes[1, 0].set_xlabel('Epoch')
        axes[1, 0].set_ylabel('Learning Rate')
        axes[1, 0].set_yscale('log')
        axes[1, 0].grid(True)
        
        # 移除空的子图
        axes[1, 1].remove()
        
        plt.tight_layout()
        plt.savefig(self.output_dir / "training_curves.png", dpi=300, bbox_inches='tight')
        plt.close()
        
        print(f"训练曲线已保存到: {self.output_dir / 'training_curves.png'}")

def main():
    """测试训练器"""
    from models import CNNTextClassifier
    
    # 创建简单模型进行测试
    model = CNNTextClassifier(vocab_size=1000, embed_dim=64, num_filters=50)
    trainer = Trainer(model)
    
    print("训练器初始化成功")
    print(f"模型参数数量: {sum(p.numel() for p in model.parameters()):,}")

if __name__ == "__main__":
    main()
