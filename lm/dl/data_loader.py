#!/usr/bin/env python3
"""
深度学习数据加载和预处理模块
"""

import os
import re
import pandas as pd
import numpy as np
import jieba
from pathlib import Path
from sklearn.model_selection import train_test_split
from torch.utils.data import Dataset
import torch
from typing import List, Tuple, Dict, Optional

class TextPreprocessor:
    """文本预处理器"""
    
    def __init__(self):
        # 停用词列表
        self.stopwords = set([
            '的', '了', '在', '是', '我', '有', '和', '就', '不', '人', '都', '一', '一个', '上', '也', '很', '到', '说', '要', '去', '你', '会', '着', '没有', '看', '好', '自己', '这'
        ])
        
    def clean_text(self, text: str) -> str:
        """清理文本"""
        if not isinstance(text, str):
            return ""
        
        # 移除特殊字符，保留中文、英文、数字
        text = re.sub(r'[^\u4e00-\u9fa5a-zA-Z0-9\s]', '', text)
        
        # 移除多余空格
        text = re.sub(r'\s+', ' ', text).strip()
        
        return text
    
    def segment_text(self, text: str, use_stopwords: bool = True) -> List[str]:
        """分词"""
        words = jieba.lcut(text)
        
        if use_stopwords:
            words = [word for word in words if word not in self.stopwords and len(word.strip()) > 0]
        
        return words
    
    def preprocess(self, text: str, segment: bool = False) -> str:
        """完整预处理流程"""
        text = self.clean_text(text)
        
        if segment:
            words = self.segment_text(text)
            return ' '.join(words)
        
        return text

class RSSDataset(Dataset):
    """RSS文章数据集"""
    
    def __init__(self, texts: List[str], labels: List[int], tokenizer=None, max_length: int = 128):
        self.texts = texts
        self.labels = labels
        self.tokenizer = tokenizer
        self.max_length = max_length
        
    def __len__(self):
        return len(self.texts)
    
    def __getitem__(self, idx):
        text = str(self.texts[idx])
        label = int(self.labels[idx])
        
        if self.tokenizer:
            # 使用BERT tokenizer
            encoding = self.tokenizer(
                text,
                truncation=True,
                padding='max_length',
                max_length=self.max_length,
                return_tensors='pt'
            )
            
            return {
                'input_ids': encoding['input_ids'].flatten(),
                'attention_mask': encoding['attention_mask'].flatten(),
                'label': torch.tensor(label, dtype=torch.long)
            }
        else:
            # 简单的文本返回
            return {
                'text': text,
                'label': torch.tensor(label, dtype=torch.long)
            }

class DataLoader:
    """数据加载器"""
    
    def __init__(self, data_dir: str = "/Users/<USER>/workplace/rssReco/out"):
        self.data_dir = data_dir
        self.preprocessor = TextPreprocessor()
        
    def load_data(self) -> Tuple[pd.DataFrame, pd.DataFrame]:
        """加载训练和测试数据"""
        train_path = Path(self.data_dir) / "train.csv"
        test_path = Path(self.data_dir) / "test.csv"
        
        if not train_path.exists() or not test_path.exists():
            raise FileNotFoundError(f"数据文件不存在: {train_path} 或 {test_path}")
        
        train_df = pd.read_csv(train_path)
        test_df = pd.read_csv(test_path)
        
        print(f"训练数据: {len(train_df)} 样本")
        print(f"测试数据: {len(test_df)} 样本")
        print(f"训练标签分布: {train_df['label'].value_counts().to_dict()}")
        print(f"测试标签分布: {test_df['label'].value_counts().to_dict()}")
        
        return train_df, test_df
    
    def preprocess_data(self, df: pd.DataFrame, segment: bool = False) -> pd.DataFrame:
        """预处理数据"""
        df = df.copy()
        df['title'] = df['title'].apply(lambda x: self.preprocessor.preprocess(x, segment=segment))
        
        # 移除空文本
        df = df[df['title'].str.len() > 0]
        
        return df
    
    def create_datasets(self, train_df: pd.DataFrame, test_df: pd.DataFrame, 
                       tokenizer=None, max_length: int = 128, 
                       val_size: float = 0.2) -> Tuple[RSSDataset, RSSDataset, RSSDataset]:
        """创建PyTorch数据集"""
        
        # 从训练集中分出验证集
        if val_size > 0:
            train_texts, val_texts, train_labels, val_labels = train_test_split(
                train_df['title'].tolist(),
                train_df['label'].tolist(),
                test_size=val_size,
                random_state=42,
                stratify=train_df['label']
            )
        else:
            train_texts = train_df['title'].tolist()
            train_labels = train_df['label'].tolist()
            val_texts, val_labels = [], []
        
        test_texts = test_df['title'].tolist()
        test_labels = test_df['label'].tolist()
        
        # 创建数据集
        train_dataset = RSSDataset(train_texts, train_labels, tokenizer, max_length)
        val_dataset = RSSDataset(val_texts, val_labels, tokenizer, max_length) if val_texts else None
        test_dataset = RSSDataset(test_texts, test_labels, tokenizer, max_length)
        
        return train_dataset, val_dataset, test_dataset
    
    def create_data_loaders(self, train_dataset: RSSDataset, val_dataset: RSSDataset,
                           test_dataset: RSSDataset, batch_size: int = 32,
                           num_workers: int = 0) -> Tuple:
        """创建数据加载器"""
        from torch.utils.data import DataLoader as TorchDataLoader

        train_loader = TorchDataLoader(
            train_dataset,
            batch_size=batch_size,
            shuffle=True,
            num_workers=num_workers
        )

        val_loader = TorchDataLoader(
            val_dataset,
            batch_size=batch_size,
            shuffle=False,
            num_workers=num_workers
        ) if val_dataset else None

        test_loader = TorchDataLoader(
            test_dataset,
            batch_size=batch_size,
            shuffle=False,
            num_workers=num_workers
        )

        return train_loader, val_loader, test_loader

def main():
    """测试数据加载"""
    loader = DataLoader()
    
    try:
        # 加载数据
        train_df, test_df = loader.load_data()
        
        # 预处理
        train_df = loader.preprocess_data(train_df, segment=True)
        test_df = loader.preprocess_data(test_df, segment=True)
        
        print(f"\n预处理后:")
        print(f"训练数据: {len(train_df)} 样本")
        print(f"测试数据: {len(test_df)} 样本")
        
        # 显示示例
        print(f"\n示例数据:")
        for i in range(3):
            print(f"标题: {train_df.iloc[i]['title']}")
            print(f"标签: {train_df.iloc[i]['label']}")
            print()
        
    except Exception as e:
        print(f"错误: {e}")

if __name__ == "__main__":
    main()
