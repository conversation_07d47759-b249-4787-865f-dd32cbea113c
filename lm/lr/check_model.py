#!/usr/bin/env python3
"""
检查导出的RSS分类模型文件
"""

import json
import os
from pathlib import Path

def check_model_file():
    """检查模型文件的结构和内容"""
    model_path = "/Users/<USER>/workplace/rssReco/out/rss_lr_model.json"
    
    if not os.path.exists(model_path):
        print(f"错误: 模型文件不存在 {model_path}")
        return
    
    try:
        with open(model_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        print('JSON结构:')
        for key in data.keys():
            print(f'  {key}: {type(data[key])}')
            if isinstance(data[key], dict):
                for subkey in data[key].keys():
                    if subkey == 'vocabulary':
                        print(f'    {subkey}: {type(data[key][subkey])} (size: {len(data[key][subkey])})')
                    elif subkey == 'idf' or subkey == 'coef':
                        print(f'    {subkey}: {type(data[key][subkey])} (length: {len(data[key][subkey])})')
                    else:
                        print(f'    {subkey}: {type(data[key][subkey])}')
        
        print(f'\n模型元数据:')
        print(f'  训练样本数: {data["meta"]["train_size"]}')
        print(f'  测试样本数: {data["meta"]["test_size"]}')
        print(f'  特征维度: {data["meta"]["feature_dim"]}')
        print(f'  准确率: {data["meta"]["metrics"]["accuracy"]:.4f}')
        print(f'  ROC-AUC: {data["meta"]["metrics"]["roc_auc"]:.4f}')
        
        print(f'\n向量化器配置:')
        vectorizer = data["vectorizer"]
        print(f'  类型: {vectorizer["type"]}')
        print(f'  N-gram范围: {vectorizer["ngram_min"]}-{vectorizer["ngram_max"]}')
        print(f'  最小文档频率: {vectorizer["min_df"]}')
        print(f'  归一化: {vectorizer["norm"]}')
        print(f'  使用IDF: {vectorizer["use_idf"]}')
        print(f'  词汇表大小: {len(vectorizer["vocabulary"])}')
        print(f'  IDF值数量: {len(vectorizer["idf"])}')
        
        print(f'\n模型参数:')
        model = data["model"]
        print(f'  类型: {model["type"]}')
        print(f'  系数数量: {len(model["coef"])}')
        print(f'  截距: {model["intercept"]:.6f}')
        
        # 显示一些词汇表示例
        print(f'\n词汇表示例 (前10个):')
        vocab_items = list(data["vectorizer"]["vocabulary"].items())[:10]
        for ngram, idx in vocab_items:
            print(f'  "{ngram}" -> {idx}')
        
        # 文件大小
        file_size = os.path.getsize(model_path)
        print(f'\n文件信息:')
        print(f'  文件大小: {file_size / (1024*1024):.2f} MB')
        print(f'  文件路径: {model_path}')
        
    except json.JSONDecodeError as e:
        print(f"JSON解析错误: {e}")
    except Exception as e:
        print(f"读取文件时出错: {e}")

if __name__ == "__main__":
    check_model_file()
