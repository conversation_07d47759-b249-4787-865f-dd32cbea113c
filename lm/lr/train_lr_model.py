#!/usr/bin/env python3
"""
RSS Article Classification - Logistic Regression Model Training Script

This script trains a logistic regression model for RSS article classification using 
TF-IDF character n-gram features and exports the model for Java integration.

Features:
- Character-level n-grams (2-5) with TF-IDF vectorization
- Logistic regression with balanced class weights
- Model evaluation with accuracy, ROC-AUC, confusion matrix
- JSON export for Java-side model inference
"""

from __future__ import annotations
import json
import os
from pathlib import Path
import pandas as pd
import numpy as np
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.linear_model import LogisticRegression
from sklearn.metrics import (
    accuracy_score, roc_auc_score, classification_report, confusion_matrix
)

# Configuration
OUT_DIR = "/Users/<USER>/workplace/rssReco/out"
SEED = 20250727

def load_data():
    """Load training and testing datasets from CSV files."""
    try:
        train_df = pd.read_csv(Path(OUT_DIR, "train.csv"))
        test_df = pd.read_csv(Path(OUT_DIR, "test.csv"))
        
        print(f"Training data loaded: {len(train_df)} samples")
        print(f"Testing data loaded: {len(test_df)} samples")
        print(f"Training label distribution: {train_df['label'].value_counts().to_dict()}")
        print(f"Testing label distribution: {test_df['label'].value_counts().to_dict()}")
        
        return train_df, test_df
    except FileNotFoundError as e:
        print(f"Error: Could not find data files in {OUT_DIR}")
        print(f"Please ensure train.csv and test.csv exist in the output directory")
        raise e
    except Exception as e:
        print(f"Error loading data: {e}")
        raise e

def create_vectorizer():
    """Create and configure TF-IDF vectorizer with character n-grams."""
    vectorizer = TfidfVectorizer(
        analyzer="char",           # Character-level analysis
        ngram_range=(2, 5),        # Character n-grams from 2 to 5
        min_df=2,                  # Filter rare n-grams (appears in at least 2 documents)
        max_features=200000,       # Limit feature space to prevent memory issues
        norm="l2",                 # L2 normalization
        use_idf=True,              # Use inverse document frequency
        smooth_idf=True,           # Add 1 to document frequencies (standard)
        sublinear_tf=False,        # Disable sublinear TF scaling for Java compatibility
    )
    return vectorizer

def train_model(X_train, y_train):
    """Train logistic regression model with balanced class weights."""
    clf = LogisticRegression(
        solver="liblinear",        # Stable solver for sparse features
        max_iter=1000,             # Maximum iterations
        class_weight="balanced",   # Handle class imbalance automatically
        random_state=SEED          # Reproducible results
    )
    
    print("Training logistic regression model...")
    clf.fit(X_train, y_train)
    print("Model training completed")
    
    return clf

def evaluate_model(clf, X_test, y_test):
    """Evaluate model performance and display metrics."""
    print("\n" + "="*50)
    print("MODEL EVALUATION")
    print("="*50)
    
    # Get predictions and probabilities
    proba = clf.predict_proba(X_test)[:, 1]
    pred = (proba >= 0.5).astype(int)
    
    # Calculate accuracy
    accuracy = accuracy_score(y_test, pred)
    print(f"Accuracy: {accuracy:.4f}")
    
    # Calculate ROC-AUC (handle edge cases)
    try:
        roc_auc = roc_auc_score(y_test, proba)
        print(f"ROC-AUC: {roc_auc:.4f}")
    except ValueError:
        roc_auc = None
        print("ROC-AUC: Cannot be calculated (need both positive and negative samples)")
    
    # Display confusion matrix
    print("\nConfusion Matrix:")
    cm = confusion_matrix(y_test, pred)
    print(cm)
    
    # Display classification report
    print("\nClassification Report:")
    print(classification_report(y_test, pred, digits=4))
    
    return accuracy, roc_auc, pred, proba

def export_model(vectorizer, clf, X_train, X_test, y_test, pred, proba, accuracy, roc_auc):
    """Export model parameters to JSON format for Java integration."""
    print("\n" + "="*50)
    print("EXPORTING MODEL")
    print("="*50)

    # Extract vectorizer parameters
    vocab = {k: int(v) for k, v in vectorizer.vocabulary_.items()}  # Convert int64 to int
    idf = vectorizer.idf_.tolist()        # IDF values aligned with column indices

    # Extract model parameters
    coef = clf.coef_.ravel().tolist()     # Coefficients aligned with column indices
    intercept = float(clf.intercept_[0])  # Model intercept
    
    # Create export dictionary
    export_data = {
        "vectorizer": {
            "type": "tfidf_char",
            "ngram_min": 2,
            "ngram_max": 5,
            "min_df": 2,
            "norm": "l2",
            "use_idf": True,
            "smooth_idf": True,
            "sublinear_tf": False,
            "vocabulary": vocab,       # key=ngram, value=column_index
            "idf": idf                # idf[i] corresponds to column i
        },
        "model": {
            "type": "logreg_binary",
            "coef": coef,             # coef[i] corresponds to column i
            "intercept": intercept
        },
        "meta": {
            "train_size": int(X_train.shape[0]),
            "test_size": int(X_test.shape[0]),
            "seed": SEED,
            "feature_dim": int(X_train.shape[1]),
            "metrics": {
                "accuracy": float(accuracy),
                "roc_auc": float(roc_auc) if roc_auc is not None else None
            }
        }
    }
    
    # Ensure output directory exists
    os.makedirs(OUT_DIR, exist_ok=True)
    
    # Save to JSON file
    output_path = Path(OUT_DIR, "rss_lr_model.json")
    with open(output_path, "w", encoding="utf-8") as f:
        json.dump(export_data, f, ensure_ascii=False, indent=2)
    
    print(f"Model exported to: {output_path}")
    print(f"Feature dimensions: {X_train.shape[1]}")
    print(f"Vocabulary size: {len(vocab)}")

def main():
    """Main training pipeline."""
    print("RSS Article Classification - Model Training")
    print("="*50)
    
    try:
        # Load data
        train_df, test_df = load_data()
        
        # Create and fit vectorizer
        print("\nCreating TF-IDF vectorizer with character n-grams...")
        vectorizer = create_vectorizer()
        
        # Transform text data to feature vectors
        print("Transforming training data...")
        X_train = vectorizer.fit_transform(train_df["title"].astype(str))
        y_train = train_df["label"].values
        
        print("Transforming testing data...")
        X_test = vectorizer.transform(test_df["title"].astype(str))
        y_test = test_df["label"].values
        
        print(f"Feature matrix shape - Train: {X_train.shape}, Test: {X_test.shape}")
        
        # Train model
        clf = train_model(X_train, y_train)
        
        # Evaluate model
        accuracy, roc_auc, pred, proba = evaluate_model(clf, X_test, y_test)
        
        # Export model for Java integration
        export_model(vectorizer, clf, X_train, X_test, y_test, pred, proba, accuracy, roc_auc)
        
        print("\n" + "="*50)
        print("TRAINING COMPLETED SUCCESSFULLY")
        print("="*50)
        
    except Exception as e:
        print(f"\nError during training: {e}")
        raise e

if __name__ == "__main__":
    main()
